@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom line-clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* NourishPlate Design System - Health & Nutrition Focused
All colors MUST be HSL for consistency and theming.
*/

@layer base {
  :root {
    /* Base colors */
    --background: 120 20% 98%;
    --foreground: 140 15% 15%;

    /* Card system */
    --card: 0 0% 100%;
    --card-foreground: 140 15% 15%;

    /* Popover system */
    --popover: 0 0% 100%;
    --popover-foreground: 140 15% 15%;

    /* Primary - Fresh green for health/nutrition */
    --primary: 142 68% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 142 68% 55%;

    /* Secondary - Soft sage */
    --secondary: 140 25% 92%;
    --secondary-foreground: 140 15% 25%;

    /* Muted system */
    --muted: 140 15% 94%;
    --muted-foreground: 140 10% 45%;

    /* Accent - Vibrant lime for highlights */
    --accent: 75 80% 55%;
    --accent-foreground: 140 15% 15%;

    /* Success - Deep green */
    --success: 140 80% 35%;
    --success-foreground: 0 0% 100%;

    /* Warning - Orange */
    --warning: 35 85% 55%;
    --warning-foreground: 0 0% 100%;

    /* Destructive */
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    /* Border and input */
    --border: 140 20% 88%;
    --input: 140 20% 92%;
    --ring: 142 68% 45%;

    --radius: 0.75rem;

    /* Gradients for beautiful backgrounds */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-health: linear-gradient(135deg, hsl(142 68% 45%), hsl(75 80% 55%));
    --gradient-card: linear-gradient(135deg, hsl(0 0% 100%), hsl(140 25% 98%));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(140 20% 96%));

    /* Shadows with health theme */
    --shadow-soft: 0 4px 20px -4px hsl(var(--primary) / 0.1);
    --shadow-glow: 0 0 30px hsl(var(--primary-glow) / 0.3);
    --shadow-card: 0 2px 10px -2px hsl(140 15% 15% / 0.08);

    /* Animation variables */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Landing Page */
.page {
  position: relative; 
  width: 100%; 
  min-height: 100vh; 
  background: #FFFFFFFF; 
}

/* Dashboard */
.dashboard-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: #FFFFFFFF;
}