// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://fqayygyorwvgekebprco.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZxYXl5Z3lvcnd2Z2VrZWJwcmNvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0MzAzMzcsImV4cCI6MjA2NzAwNjMzN30.WWhLo5ibOBVAjCLCWypds7W3LTzzWeoSPHkz3kS7hxY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});