// Vercel Edge Function for sending family invitation emails
// This handles CORS and calls Resend API securely from the server side

export const config = {
  runtime: 'edge',
};

export default async function handler(request) {
  // Handle CORS
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  if (request.method !== 'POST') {
    return new Response(JSON.stringify({
      success: false,
      error: 'Method not allowed'
    }), { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } });
  }

  // MINIMAL TEST VERSION - bypass all complex logic
  try {
    const body = await request.json();

    // Just return success for now to test if the function works
    return new Response(JSON.stringify({
      success: true,
      message: 'Function is working - email sending temporarily disabled for debugging',
      receivedData: body
    }), { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } });

  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: `Parse error: ${error.message}`
    }), { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } });
  }


}
